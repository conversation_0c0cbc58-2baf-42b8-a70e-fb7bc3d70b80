use chrono::{DateTime, Utc};
use mongodb::bson::oid::ObjectId;
use serde::{Deserialize, Serialize, Deserializer};

// Função helper para deserializar datas de forma robusta
fn deserialize_datetime<'de, D>(deserializer: D) -> Result<DateTime<Utc>, D::Error>
where
    D: Deserializer<'de>,
{
    use serde::de::Error;
    use serde_json::Value;

    let value: Value = Value::deserialize(deserializer)?;

    match value {
        Value::String(s) => {
            DateTime::parse_from_rfc3339(&s)
                .or_else(|_| DateTime::parse_from_str(&s, "%Y-%m-%dT%H:%M:%S%.fZ"))
                .or_else(|_| DateTime::parse_from_str(&s, "%Y-%m-%d %H:%M:%S"))
                .map(|dt| dt.with_timezone(&Utc))
                .map_err(|e| Error::custom(format!("Erro ao parsear data '{}': {}", s, e)))
        }
        _ => {
            Ok(Utc::now())
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RechargeSale {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub erp_client_id: String,
    pub erp_employee_id: String,
    pub package_type: String,
    pub quantity: u32,
    pub recharge_code_ids: Vec<ObjectId>,
    pub total_sale_price: f64,
    pub payment_method: String,
    #[serde(deserialize_with = "deserialize_datetime")]
    pub sale_timestamp: DateTime<Utc>,
}