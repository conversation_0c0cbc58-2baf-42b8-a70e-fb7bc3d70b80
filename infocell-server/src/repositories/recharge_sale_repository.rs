use crate::{
    error::{AppError, Result},
    models::RechargeSale,
};
use bson::{doc, oid::ObjectId, Bson};
use chrono::{DateTime, Utc};
use futures_util::stream::TryStreamExt;
use mongodb::{Collection, Database, ClientSession};

#[derive(Clone)]
pub struct RechargeSaleRepository {
    collection: Collection<RechargeSale>,
}

impl RechargeSaleRepository {
    pub fn new(database: &Database) -> Self {
        Self {
            collection: database.collection("recharge_sales"),
        }
    }

    pub async fn create(&self, sale: &RechargeSale, session: Option<&mut ClientSession>) -> Result<ObjectId> {
        let action = self.collection.insert_one(sale);

        let result = match session {
            Some(session) => action.session(session).await.map_err(AppError::DatabaseError)?,
            None => action.await.map_err(AppError::DatabaseError)?,
        };

        result
            .inserted_id
            .as_object_id()
            .ok_or_else(|| AppError::InternalServerError("Failed to get sale ID".to_string()))
    }

    pub async fn find_all(
        &self,
        page: u32,
        per_page: u32,
        erp_client_id: Option<String>,
        erp_employee_id: Option<String>,
        start_date: Option<DateTime<Utc>>,
        end_date: Option<DateTime<Utc>>,
    ) -> Result<(Vec<RechargeSale>, u64)> {
        let mut filter = doc! {};
        if let Some(client_id) = erp_client_id {
            filter.insert("erp_client_id", client_id);
        }
        if let Some(employee_id) = erp_employee_id {
            filter.insert("erp_employee_id", employee_id);
        }

        let mut date_filter = doc! {};
        if let Some(start) = start_date {
            date_filter.insert("$gte", Bson::DateTime(start.into()));
        }
        if let Some(end) = end_date {
            date_filter.insert("$lt", Bson::DateTime(end.into()));
        }

        if !date_filter.is_empty() {
            filter.insert("sale_timestamp", date_filter);
        }

        let skip = ((page - 1) * per_page) as u64;
        let total = self.collection.count_documents(filter.clone()).await?;

        let find_options = mongodb::options::FindOptions::builder()
            .skip(skip)
            .limit(per_page as i64)
            .sort(doc! { "sale_timestamp": -1 })
            .build();
            
        let mut cursor = self.collection.find(filter).with_options(find_options).await?;
        let mut sales = Vec::new();
        while let Some(sale) = cursor.try_next().await? {
            sales.push(sale);
        }

        Ok((sales, total))
    }
} 