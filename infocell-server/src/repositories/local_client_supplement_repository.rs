use mongodb::{
    bson::{doc, oid::ObjectId, Document, to_bson, Bson},
    options::{FindOneOptions, UpdateOptions},
    Collection, Database,
};
use chrono::Utc;

use crate::error::{AppError, Result};
use crate::models::local_client_data::LocalClientSupplement;

const COLLECTION_NAME: &str = "local_client_supplements";

#[derive(Clone)]
pub struct LocalClientSupplementRepository {
    collection: Collection<LocalClientSupplement>,
}

impl LocalClientSupplementRepository {
    pub fn new(db: &Database) -> Self {
        Self {
            collection: db.collection(COLLECTION_NAME),
        }
    }

    pub async fn find_by_erp_client_id(&self, erp_client_id: &str) -> Result<Option<LocalClientSupplement>> {
        let filter = doc! { "erp_client_id": erp_client_id };
        
        tracing::debug!("Buscando dados suplementares para erp_client_id: {}", erp_client_id);
        
        match self.collection.find_one(filter).await {
            Ok(result) => {
                tracing::debug!("Dados encontrados para erp_client_id {}: {}", erp_client_id, result.is_some());
                Ok(result)
            }
            Err(e) => {
                tracing::error!("Erro ao buscar dados suplementares por erp_client_id '{}': {}", erp_client_id, e);
                tracing::error!("Tipo do erro: {:?}", &e.kind);
                
                if let mongodb::error::ErrorKind::BsonDeserialization(ref bson_err) = *e.kind {
                    tracing::error!("Erro de deserialização BSON: {:?}", bson_err);
                }
                
                Err(AppError::DatabaseError(e.into()))
            }
        }
    }

    /// Cria ou atualiza completamente um registro de dados suplementares de cliente.
    /// Se um documento com o `erp_client_id` existir, ele será substituído.
    /// Caso contrário, um novo documento será inserido.
    /// O campo `updated_at` é atualizado e `created_at` é definido se for uma nova inserção.
    pub async fn upsert_by_erp_client_id(&self, supplement_data: &mut LocalClientSupplement) -> Result<String> {
        let filter = doc! { "erp_client_id": &supplement_data.erp_client_id };

        // Verificar se o documento já existe para definir created_at corretamente
        let existing_doc = self.collection.find_one(filter.clone()).await?; // Seguindo rustc: remover ", None"

        if existing_doc.is_none() {
            supplement_data.created_at = Utc::now();
        } else {
            // Manter o created_at original se o documento já existe
            supplement_data.created_at = existing_doc.unwrap().created_at;
        }
        supplement_data.updated_at = Utc::now();
        
        // Usar update_one com $set para realizar o upsert.
        // Serializar `supplement_data` para um Document BSON para usar com $set.
        // O campo `id` (ObjectId) será gerenciado pelo MongoDB.
        // `created_at` será definido com $setOnInsert.
        // `updated_at` será sempre definido com $set.

        let mut set_doc = Document::new();
        
        // Serializar todos os campos de supplement_data para BSON, exceto 'id' e 'created_at' (que são especiais)
        // e 'erp_client_id' (que está no filtro).
        // A struct LocalClientSupplement já tem `#[serde(rename = "_id", skip_serializing_if = "Option::is_none")] pub id: Option<ObjectId>,`
        // então `to_bson` irá serializar `id` como `_id` se presente.
        // Para $set, não queremos incluir `_id` explicitamente se ele for None.
        
        if let Bson::Document(mut data_doc) = to_bson(&supplement_data)
            .map_err(|e| AppError::InternalServerError(format!("Falha ao serializar supplement_data para BSON: {}", e)))? {
            
            data_doc.remove("erp_client_id"); // Já está no filtro
            data_doc.remove("created_at");    // Será tratado por $setOnInsert
            if supplement_data.id.is_none() { // Não incluir _id no $set se for uma inserção nova (id é None)
                data_doc.remove("_id");
            }
            set_doc.insert("$set", data_doc);
        } else {
            return Err(AppError::InternalServerError("Falha ao converter supplement_data para BSON Document".to_string()));
        }

        // Adicionar $setOnInsert para created_at
        set_doc.insert("$setOnInsert", doc! { "created_at": supplement_data.created_at });
        
        // Usar update_one com opções de upsert
        let update_options = UpdateOptions::builder().upsert(true).build();
        
        let update_result = self
            .collection
            .update_one(filter.clone(), set_doc)
            .with_options(update_options)
            .await
            .map_err(|e| {
                tracing::error!("Erro ao tentar atualizar dados suplementares para erp_client_id '{}': {}", supplement_data.erp_client_id, e);
                AppError::DatabaseError(e.into())
            })?;

        // Com upsert ativado, se houve uma inserção, upserted_id conterá o novo ObjectId
        if let Some(upserted_id) = update_result.upserted_id {
            if let Some(oid) = upserted_id.as_object_id() {
                supplement_data.id = Some(oid);
                return Ok(oid.to_hex());
            }
        }

        // Se foi uma atualização (matched_count > 0), verificar se já temos o ID
        if update_result.matched_count > 0 {
            if let Some(id_obj) = &supplement_data.id {
                return Ok(id_obj.to_hex());
            } else {
                // Buscar o documento para obter o ID
                if let Some(updated_doc) = self.collection.find_one(doc! { "erp_client_id": &supplement_data.erp_client_id }).await? {
                    if let Some(id_obj) = updated_doc.id {
                        supplement_data.id = Some(id_obj);
                        return Ok(id_obj.to_hex());
                    }
                }
            }
        }
        
        // Fallback se algo inesperado ocorrer
        Err(AppError::InternalServerError("Operação de upsert concluída, mas ID não pôde ser determinado.".to_string()))
    }
}