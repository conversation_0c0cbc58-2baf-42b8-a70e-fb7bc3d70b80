"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { cn } from "@/lib/utils";
import {
  Home,
  FileText,
  FolderOpen,
  Zap,
  Archive,
  ShoppingCart,
  Boxes,
  Package,
  ChevronDown,
  LineChart,
  Import,
} from "lucide-react";
import { Calculator } from "lucide-react";

const navigationItems = [
  {
    title: "Dashboard",
    href: "/dashboard-infocell",
    icon: Home,
  },
  {
    title: "Recargas",
    icon: Zap,
    subItems: [
      {
        title: "Nova Venda",
        href: "/dashboard-infocell/recharges/new-sale",
        icon: ShoppingCart,
      },
      {
        title: "Estoque",
        href: "/dashboard-infocell/recharges/stock",
        icon: Boxes,
      },
      {
        title: "Importar Estoque",
        href: "/dashboard-infocell/recharges/import",
        icon: Import,
      },
      {
        title: "Pacotes",
        href: "/dashboard-infocell/recharges/packages",
        icon: Package,
      },
      {
        title: "Relatórios",
        href: "/dashboard-infocell/recharges/reports",
        icon: LineChart,
      },
    ],
  },
  {
    title: "Gerar Documento",
    href: "/dashboard-infocell/gerar-documento",
    icon: FileText,
  },
  {
    title: "Templates",
    href: "/dashboard-infocell/templates",
    icon: FolderOpen,
  },
  {
    title: "Documentos",
    href: "/dashboard-infocell/documentos",
    icon: Archive,
  },
  {
    title: "Calculadora Parcelamento",
    href: "/dashboard-infocell/calculadora-parcelamento",
    icon: Calculator,
  },
  // {
  //   title: "Currículo (Legado)",
  //   href: "/dashboard-infocell/resume-generator",
  //   icon: FileText,
  // },
];

export default function Sidebar() {
  const pathname = usePathname();
  const [openSubmenus, setOpenSubmenus] = useState({});

  const toggleSubmenu = (title) => {
    setOpenSubmenus((prev) => ({ ...prev, [title]: !prev[title] }));
  };

  return (
    <div className="w-64 bg-card border-r border-border h-full">
      <div className="p-6">
        <h2 className="text-lg font-semibold text-foreground mb-6">
          Infocell Tools
        </h2>

        <nav className="space-y-2">
          {navigationItems.map((item) => {
            if (item.subItems) {
              const isSubmenuActive = item.subItems.some((sub) =>
                pathname.startsWith(sub.href)
              );
              const isOpen = openSubmenus[item.title] || isSubmenuActive;

              return (
                <div key={item.title}>
                  <button
                    onClick={() => toggleSubmenu(item.title)}
                    className={cn(
                      "flex items-center justify-between w-full gap-3 px-3 py-2 text-sm rounded-md transition-colors",
                      "hover:bg-muted hover:text-foreground",
                      isSubmenuActive
                        ? "bg-primary text-primary-foreground"
                        : "text-muted-foreground"
                    )}
                  >
                    <div className="flex items-center gap-3">
                      <item.icon className="h-4 w-4" />
                      {item.title}
                    </div>
                    <ChevronDown
                      className={cn("h-4 w-4 transition-transform", {
                        "rotate-180": isOpen,
                      })}
                    />
                  </button>
                  {isOpen && (
                    <div className="pl-6 mt-1 space-y-1">
                      {item.subItems.map((subItem) => {
                        const isSubItemActive = pathname === subItem.href;
                        return (
                          <Link
                            key={subItem.href}
                            href={subItem.href}
                            className={cn(
                              "flex items-center gap-3 px-3 py-2 text-sm rounded-md transition-colors",
                              "hover:bg-muted hover:text-foreground",
                              isSubItemActive
                                ? "bg-primary text-primary-foreground"
                                : "text-muted-foreground"
                            )}
                          >
                            <subItem.icon className="h-4 w-4" />
                            {subItem.title}
                          </Link>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            }

            const isActive = pathname === item.href;
            const Icon = item.icon;

            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  "flex items-center gap-3 px-3 py-2 text-sm rounded-md transition-colors",
                  "hover:bg-muted hover:text-foreground",
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "text-muted-foreground"
                )}
              >
                <Icon className="h-4 w-4" />
                {item.title}
              </Link>
            );
          })}
        </nav>
      </div>
    </div>
  );
} 