"use client";

import React, { useRef } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Printer, Share2 } from 'lucide-react';
import { toast } from "sonner";
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

const RechargeReceipt = ({ saleReceipt, selectedPackage, selectedClient, selectedEmployee, onNewSale }) => {
    const receiptRef = useRef(null);

    const generateReceiptText = React.useCallback(() => {
        if (!saleReceipt || !saleReceipt.sale || !selectedPackage || !selectedClient || !selectedEmployee) return '';
        const { sale, codes } = saleReceipt;
        let text = `COMPROVANTE DE VENDA - INFOCELL\n\n`;
        text += `--------------------------------\n`;
        text += `CLIENTE\n`;
        text += `Nome: ${selectedClient.nome}\n\n`;
        text += `VENDEDOR\n`;
        text += `Nome: ${selectedEmployee.nome}\n\n`;
        text += `DETALHES DA VENDA\n`;
        text += `Data: ${new Date(sale.sale_timestamp).toLocaleString('pt-BR')}\n`;
        text += `Forma de Pagamento: ${sale.payment_method}\n`;
        text += `--------------------------------\n\n`;
        text += `ITENS\n`;
        text += `Pacote: ${selectedPackage.package_name} (x${codes.length})\n`;
        text += `Validade: ${selectedPackage.validity_days} dias\n`;
        text += `Códigos de Recarga:\n`;
        codes.forEach(c => { text += `  - ${c.code}\n`; });
        text += `\n--------------------------------\n`;
        text += `TOTAL: R$ ${sale.total_sale_price.toFixed(2)}\n`;
        return text;
    }, [saleReceipt, selectedPackage, selectedClient, selectedEmployee]);

    const handleCopy = () => {
        navigator.clipboard.writeText(generateReceiptText()).then(() => {
            toast.success("Recibo copiado para a área de transferência.");
        }, () => {
            toast.error("Falha ao copiar o recibo.");
        });
    };

    const handlePrint = () => {
        window.print();
    };

    const handleShare = async () => {
        if (!receiptRef.current) return;
        toast.info("Gerando PDF para compartilhamento...");

        try {
            const canvas = await html2canvas(receiptRef.current, { scale: 2 });
            const imgData = canvas.toDataURL('image/png');
            
            const pdf = new jsPDF('p', 'mm', 'a4'); // Formato A4
            const pdfWidth = pdf.internal.pageSize.getWidth();
            const pdfHeight = (canvas.height * pdfWidth) / canvas.width;
            
            pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);
            const pdfBlob = pdf.output('blob');

            const pdfFile = new File([pdfBlob], 'recibo-infocell.pdf', { type: 'application/pdf' });

            if (navigator.canShare && navigator.canShare({ files: [pdfFile] })) {
                await navigator.share({
                    files: [pdfFile],
                    title: 'Recibo Infocell',
                    text: 'Recibo da sua compra na Infocell.',
                });
                toast.success("Recibo compartilhado!");
            } else {
                 // Fallback para download se o compartilhamento não for suportado
                const link = document.createElement('a');
                link.href = URL.createObjectURL(pdfBlob);
                link.download = 'recibo-infocell.pdf';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                toast.success("PDF do recibo baixado!");
            }
        } catch (error) {
            console.error("Erro ao gerar/compartilhar PDF:", error);
            toast.error("Ocorreu um erro ao tentar gerar o PDF.");
        }
    };

    if (!saleReceipt || !saleReceipt.sale) {
        return <p>Carregando recibo...</p>;
    }
    
    const { sale, codes } = saleReceipt;

    return (
        <div className="flex flex-col items-center w-full">
            <style jsx global>{`
                @media print {
                    body * {
                        visibility: hidden;
                    }
                    #printable-receipt, #printable-receipt * {
                        visibility: visible;
                    }
                    #printable-receipt {
                        position: absolute;
                        left: 0;
                        top: 0;
                        margin: 0;
                        padding: 0;
                        width: 100%;
                    }
                }
                @page { 
                    size: a4;
                    margin: 20mm;
                }
            `}</style>
            
            <Card className="w-full max-w-4xl no-print mb-4">
                 <CardHeader className="text-center">
                    <CheckCircle className="mx-auto h-12 w-12 text-green-500" />
                    <CardTitle className="mt-4">Venda Realizada com Sucesso!</CardTitle>
                </CardHeader>
            </Card>

            {/* A4 Preview - hidden on main screen, used for PDF generation and print */}
            <div className="absolute top-0 opacity-0 h-0 overflow-hidden -z-10">
                <div id="printable-receipt" ref={receiptRef} className="bg-white text-black p-8 shadow-lg w-[210mm] h-[297mm]">
                    {/* Header */}
                    <div className="flex justify-between items-center border-b pb-4">
                        <img src="/logo-infocell.png" alt="Logo Infocell" className="h-16" />
                        <div className="text-right">
                            <h1 className="text-2xl font-bold">COMPROVANTE DE VENDA</h1>
                            <p className="text-sm">Data: {new Date(sale.sale_timestamp).toLocaleString('pt-BR')}</p>
                        </div>
                    </div>

                    {/* Details */}
                    <div className="grid grid-cols-2 gap-8 my-8">
                        <div>
                            <h2 className="font-bold border-b mb-2">CLIENTE</h2>
                            <p>{selectedClient?.nome || 'N/A'}</p>
                        </div>
                         <div>
                            <h2 className="font-bold border-b mb-2">VENDEDOR</h2>
                            <p>{selectedEmployee?.nome || 'N/A'}</p>
                        </div>
                    </div>

                    {/* Items Table */}
                    <div className="mb-8">
                         <h2 className="font-bold border-b mb-2">ITENS DA VENDA</h2>
                        <table className="w-full text-left">
                            <thead>
                                <tr className="bg-gray-100">
                                    <th className="p-2">Descrição</th>
                                    <th className="p-2">Validade</th>
                                    <th className="p-2 text-right">Qtd.</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td className="p-2 align-top">{selectedPackage?.package_name}</td>
                                    <td className="p-2 align-top">{selectedPackage?.validity_days} dias</td>
                                    <td className="p-2 align-top text-right">{codes.length}</td>
                                </tr>
                                 <tr>
                                    <td colSpan="3" className="px-2 pt-1 pb-4">
                                        <p className="font-semibold text-xs">Códigos de Recarga:</p>
                                        <div className="pl-4 text-xs font-mono tracking-wider">
                                            {codes.map((c, index) => <p key={c.id?.$oid || c._id?.$oid || index}>{c.code}</p>)}
                                        </div>
                                    </td>
                                 </tr>
                            </tbody>
                        </table>
                    </div>

                    {/* Footer */}
                    <div className="flex justify-end border-t pt-4">
                        <div className="w-1/3">
                            <div className="flex justify-between">
                                <span className="font-bold">Pagamento:</span>
                                <span>{sale.payment_method}</span>
                            </div>
                            <div className="flex justify-between mt-2 font-bold text-lg">
                                <span>TOTAL:</span>
                                <span>R$ {sale.total_sale_price.toFixed(2)}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Ações */}
            <div className="flex flex-col sm:flex-row gap-2 mt-4 w-full max-w-md no-print">
                <Button onClick={handleCopy} variant="outline" className="w-full">
                    <Copy className="mr-2 h-4 w-4"/> Copiar Texto
                </Button>
                <Button onClick={handlePrint} variant="outline" className="w-full">
                    <Printer className="mr-2 h-4 w-4"/> Imprimir
                </Button>
                <Button onClick={handleShare} className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                    <Share2 className="mr-2 h-4 w-4"/> Compartilhar / PDF
                </Button>
            </div>
            <Button onClick={onNewSale} className="w-full mt-2 max-w-md no-print">
                <ArrowLeft className="mr-2 h-4 w-4"/> Nova Venda
            </Button>
        </div>
    );
};

export default RechargeReceipt; 

