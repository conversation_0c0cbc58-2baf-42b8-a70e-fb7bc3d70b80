'use client';

import { useState, useEffect, useRef } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Search, User, Plus, Check, UserPlus } from "lucide-react";
import infocell<PERSON>pi from "@/lib/infocellApi";

export default function ClientSearchInput({ 
  onSelect, 
  selectedClient, 
  className = "",
  showCreateOption = false,
  onCreateNewClient = null
}) {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [error, setError] = useState(null);
  
  const searchTimeoutRef = useRef(null);
  const containerRef = useRef(null);

  // Fechar dropdown ao clicar fora
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setShowResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Buscar clientes com debounce
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (searchTerm.trim().length >= 2) {
      searchTimeoutRef.current = setTimeout(async () => {
        await searchClients(searchTerm.trim());
      }, 300); // 300ms de debounce
    } else {
      setSearchResults([]);
      setShowResults(false);
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchTerm]);

  const searchClients = async (term) => {
    try {
      setIsSearching(true);
      setError(null);
      
      const response = await infocellApi.getClients({ nome: term, per_page: 10 });
      setSearchResults(response.data?.clients || []);
      setShowResults(true);
    } catch (err) {
      console.error('Erro ao buscar clientes:', err);
      setError('Erro ao buscar clientes: ' + err.message);
      setSearchResults([]);
      setShowResults(false);
    } finally {
      setIsSearching(false);
    }
  };

  const handleSelectClient = (client) => {
    onSelect(client);
    setSearchTerm('');
    setShowResults(false);
    setSearchResults([]);
  };

  const handleClearSelection = () => {
    onSelect(null);
    setSearchTerm('');
    setShowResults(false);
    setSearchResults([]);
  };

  const handleCreateNewClient = () => {
    if (onCreateNewClient) {
      onCreateNewClient();
      setShowResults(false);
      setSearchTerm('');
    }
  };

  const formatClientDisplay = (client) => {
    const document = client.cpf || client.cnpj || '';
    return `${client.nome}${document ? ` - ${document}` : ''}`;
  };

  const formatClientType = (client) => {
    if (client.tipo_pessoa === 'PF') return 'Pessoa Física';
    if (client.tipo_pessoa === 'PJ') return 'Pessoa Jurídica';
    if (client.tipo_pessoa === 'ES') return 'Estrangeiro';
    return client.tipo_pessoa || '';
  };

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* Cliente Selecionado */}
      {selectedClient ? (
        <div className="flex items-center justify-between p-3 border rounded-md bg-green-50 border-green-200">
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded-full">
              <Check className="h-4 w-4 text-green-600" />
            </div>
            <div>
              <p className="font-medium text-green-900">{selectedClient.nome}</p>
              <p className="text-sm text-green-700">
                {formatClientType(selectedClient)}
                {(selectedClient.cpf || selectedClient.cnpj) && ` • ${selectedClient.cpf || selectedClient.cnpj}`}
              </p>
            </div>
          </div>
          <Button variant="outline" size="sm" onClick={handleClearSelection}>
            Alterar
          </Button>
        </div>
      ) : (
        <>
          {/* Campo de Busca */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Buscar Cliente por Nome"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onFocus={() => searchResults.length > 0 && setShowResults(true)}
              className="pl-10"
            />
          </div>

          {/* Resultados da Busca */}
          {showResults && (
            <Card className="absolute top-full left-0 right-0 z-50 mt-1 max-h-80 overflow-y-auto">
              <CardContent className="p-0">
                {isSearching ? (
                  <div className="p-3 text-center text-muted-foreground">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mx-auto mb-2"></div>
                    Buscando...
                  </div>
                ) : searchResults.length > 0 ? (
                  <div className="divide-y">
                    {searchResults.map((client) => (
                      <button
                        key={client.id}
                        onClick={() => handleSelectClient(client)}
                        className="w-full p-3 text-left hover:bg-muted/50 transition-colors focus:outline-none focus:bg-muted/50"
                      >
                        <div className="flex items-center gap-3">
                          <div className="flex items-center justify-center w-8 h-8 bg-muted rounded-full">
                            <User className="h-4 w-4 text-muted-foreground" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="font-medium text-foreground truncate">
                              {client.nome}
                            </p>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <span>{formatClientType(client)}</span>
                              {(client.cpf || client.cnpj) && (
                                <>
                                  <span>•</span>
                                  <span>{client.cpf || client.cnpj}</span>
                                </>
                              )}
                              {client.email && (
                                <>
                                  <span>•</span>
                                  <span className="truncate">{client.email}</span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                ) : (
                  <div className="p-3">
                    <div className="text-center text-muted-foreground mb-3">
                      <User className="h-8 w-8 mx-auto mb-2" />
                      <p>Nenhum cliente encontrado</p>
                      <p className="text-sm">Tente buscar por nome</p>
                    </div>
                    
                    {/* Opção para criar novo cliente */}
                    {showCreateOption && onCreateNewClient && (
                      <>
                        <div className="border-t pt-3">
                          <Button
                            onClick={handleCreateNewClient}
                            variant="outline"
                            className="w-full justify-start gap-2 text-blue-600 border-blue-200 hover:bg-blue-50"
                          >
                            <UserPlus className="h-4 w-4" />
                            Criar cliente a partir dos dados do formulário
                          </Button>
                          <p className="text-xs text-muted-foreground mt-2 text-center">
                            O cliente será cadastrado no ERP automaticamente
                          </p>
                        </div>
                      </>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </>
      )}

      {/* Mensagem de Erro */}
      {error && (
        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Dica de Uso */}
      {!selectedClient && !showResults && searchTerm.length === 0 && (
        <p className="mt-2 text-xs text-muted-foreground">
          Digite pelo menos 2 caracteres para buscar clientes
        </p>
      )}
    </div>
  );
}