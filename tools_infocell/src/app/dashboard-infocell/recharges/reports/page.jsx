"use client";

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { CalendarDays, Download, Search, Filter, X, AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import ClientSearchInput from '@/components/custom/ClientSearchInput';
import EmployeeSearchInput from '@/components/custom/EmployeeSearchInput';
import infocellApi from '@/lib/infocellApi';
import { toast } from 'sonner';
import { useDebounce } from '@/lib/hooks/useDebounce';

const ReportsPage = () => {
    const [filters, setFilters] = useState({
        page: 1,
        per_page: 25,
        erp_client_id: '',
        erp_employee_id: '',
        start_date: '',
        end_date: '',
        payment_method: '',
        min_value: '',
        max_value: '',
        search_text: '',
        sort_by: 'sale_timestamp',
        sort_order: 'desc'
    });
    const [sales, setSales] = useState([]);
    const [totalSales, setTotalSales] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [packagesMap, setPackagesMap] = useState(new Map());
    const [employeesMap, setEmployeesMap] = useState(new Map());
    const [clientsMap, setClientsMap] = useState(new Map());


    const debouncedFilters = useDebounce(filters, 500);

    // Funções auxiliares para filtros de data
    const getDatePresets = () => {
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        const lastWeek = new Date(today);
        lastWeek.setDate(lastWeek.getDate() - 7);

        const lastMonth = new Date(today);
        lastMonth.setMonth(lastMonth.getMonth() - 1);

        return {
            today: {
                start_date: today.toISOString().split('T')[0],
                end_date: today.toISOString().split('T')[0]
            },
            yesterday: {
                start_date: yesterday.toISOString().split('T')[0],
                end_date: yesterday.toISOString().split('T')[0]
            },
            lastWeek: {
                start_date: lastWeek.toISOString().split('T')[0],
                end_date: today.toISOString().split('T')[0]
            },
            lastMonth: {
                start_date: lastMonth.toISOString().split('T')[0],
                end_date: today.toISOString().split('T')[0]
            }
        };
    };

    const applyDatePreset = (preset) => {
        const presets = getDatePresets();
        if (presets[preset]) {
            setFilters(prev => ({
                ...prev,
                ...presets[preset],
                page: 1
            }));
        }
    };

    useEffect(() => {
        const fetchInitialData = async () => {
            try {
                const [packagesResponse, employeesResponse] = await Promise.all([
                    infocellApi.getRechargePackages(),
                    infocellApi.getEmployees({ per_page: 500 }) // Busca um número grande de funcionários
                ]);
                
                const newPackagesMap = new Map(packagesResponse.data.map(pkg => [pkg.package_type, pkg.package_name]));
                setPackagesMap(newPackagesMap);

                const newEmployeesMap = new Map(employeesResponse.data.map(emp => [
                    emp.id || emp._id?.$oid || emp._id,
                    emp.nome
                ]));
                setEmployeesMap(newEmployeesMap);

            } catch (error) {
                toast.error("Falha ao carregar dados iniciais (pacotes ou vendedores).");
            }
        };
        fetchInitialData();
    }, []);

    const fetchReports = useCallback(async (currentFilters) => {
        setIsLoading(true);
        try {
            const params = { ...currentFilters };

            // Clean empty filters
            if (!params.erp_client_id) delete params.erp_client_id;
            if (!params.erp_employee_id) delete params.erp_employee_id;
            if (!params.payment_method) delete params.payment_method;
            if (!params.min_value) delete params.min_value;
            if (!params.max_value) delete params.max_value;
            if (!params.search_text) delete params.search_text;

            // Format dates to ISO 8601 with time for the backend
            if (params.start_date) {
                params.start_date = new Date(params.start_date).toISOString();
            } else {
                delete params.start_date;
            }
            if (params.end_date) {
                // Set to end of day
                const endDate = new Date(params.end_date);
                endDate.setHours(23, 59, 59, 999);
                params.end_date = endDate.toISOString();
            } else {
                delete params.end_date;
            }

            const response = await infocellApi.getSalesReports(params);
            const salesData = response.data.sales;
            setSales(salesData);
            setTotalSales(response.data.total);

            // Fetch client names for new sales
            const newClientsMap = new Map(clientsMap);
            for (const sale of salesData) {
                if (!newClientsMap.has(sale.erp_client_id)) {
                    try {
                        const clientResponse = await infocellApi.getClient(sale.erp_client_id);
                        const clientName = clientResponse.data?.nome || clientResponse.nome || 'Nome não encontrado';
                        newClientsMap.set(sale.erp_client_id, clientName);
                    } catch (e) {
                        console.error(`Falha ao buscar cliente ${sale.erp_client_id}`, e);
                        newClientsMap.set(sale.erp_client_id, 'Não encontrado');
                    }
                }
            }
            setClientsMap(newClientsMap);



        } catch (error) {
            toast.error(`Falha ao buscar relatórios: ${error.message}`);
        } finally {
            setIsLoading(false);
        }
    }, [toast]);

    useEffect(() => {
        fetchReports(debouncedFilters);
    }, [debouncedFilters, fetchReports]);

    const handleFilterChange = (key, value) => {
        setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
    };

    const handleClientSelect = (client) => {
        handleFilterChange('erp_client_id', client ? client.id : '');
    };

    const handleEmployeeSelect = (employee) => {
        handleFilterChange('erp_employee_id', employee ? employee.id : '');
    };

    const handleSortChange = (column) => {
        setFilters(prev => ({
            ...prev,
            sort_by: column,
            sort_order: prev.sort_by === column && prev.sort_order === 'asc' ? 'desc' : 'asc',
            page: 1
        }));
    };

    const clearAllFilters = () => {
        setFilters({
            page: 1,
            per_page: 25,
            erp_client_id: '',
            erp_employee_id: '',
            start_date: '',
            end_date: '',
            payment_method: '',
            min_value: '',
            max_value: '',
            search_text: '',
            sort_by: 'sale_timestamp',
            sort_order: 'desc'
        });
    };

    const summary = useMemo(() => {
        return sales.reduce((acc, sale) => acc + sale.total_sale_price, 0);
    }, [sales]);

    const totalPages = Math.ceil(totalSales / filters.per_page);

    const getSortIcon = (column) => {
        if (filters.sort_by !== column) return '↕️';
        return filters.sort_order === 'asc' ? '↑' : '↓';
    };

    const formatCurrency = (value) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).format(value);
    };

    // Função para gerar badges dos filtros ativos
    const getActiveFilters = () => {
        const activeFilters = [];

        if (filters.start_date || filters.end_date) {
            let dateText = 'Período: ';
            if (filters.start_date && filters.end_date) {
                dateText += `${new Date(filters.start_date).toLocaleDateString('pt-BR')} até ${new Date(filters.end_date).toLocaleDateString('pt-BR')}`;
            } else if (filters.start_date) {
                dateText += `a partir de ${new Date(filters.start_date).toLocaleDateString('pt-BR')}`;
            } else {
                dateText += `até ${new Date(filters.end_date).toLocaleDateString('pt-BR')}`;
            }
            activeFilters.push({
                key: 'date',
                label: dateText,
                onRemove: () => {
                    setFilters(prev => ({ ...prev, start_date: '', end_date: '', page: 1 }));
                }
            });
        }

        if (filters.payment_method) {
            activeFilters.push({
                key: 'payment',
                label: `Pagamento: ${filters.payment_method}`,
                onRemove: () => {
                    setFilters(prev => ({ ...prev, payment_method: '', page: 1 }));
                }
            });
        }

        if (filters.search_text) {
            activeFilters.push({
                key: 'search',
                label: `Busca: "${filters.search_text}"`,
                onRemove: () => {
                    setFilters(prev => ({ ...prev, search_text: '', page: 1 }));
                }
            });
        }

        if (filters.erp_client_id) {
            activeFilters.push({
                key: 'client',
                label: 'Cliente selecionado',
                onRemove: () => {
                    setFilters(prev => ({ ...prev, erp_client_id: '', page: 1 }));
                }
            });
        }

        if (filters.erp_employee_id) {
            activeFilters.push({
                key: 'employee',
                label: 'Vendedor selecionado',
                onRemove: () => {
                    setFilters(prev => ({ ...prev, erp_employee_id: '', page: 1 }));
                }
            });
        }

        return activeFilters;
    };

    const exportToPDF = async () => {
        try {
            // Importação dinâmica para evitar problemas de SSR
            const { default: jsPDF } = await import('jspdf');
            const { default: autoTable } = await import('jspdf-autotable');

            const doc = new jsPDF();

            // Cabeçalho do relatório
            doc.setFontSize(18);
            doc.text('Relatório de Vendas de Recargas', 14, 22);

            doc.setFontSize(12);
            doc.text(`Gerado em: ${new Date().toLocaleString('pt-BR')}`, 14, 32);

            // Informações dos filtros aplicados
            let filterInfo = [];
            if (filters.start_date) filterInfo.push(`Data inicial: ${new Date(filters.start_date).toLocaleDateString('pt-BR')}`);
            if (filters.end_date) filterInfo.push(`Data final: ${new Date(filters.end_date).toLocaleDateString('pt-BR')}`);
            if (filters.payment_method) filterInfo.push(`Pagamento: ${filters.payment_method}`);
            if (filters.search_text) filterInfo.push(`Busca: ${filters.search_text}`);

            if (filterInfo.length > 0) {
                doc.setFontSize(10);
                doc.text('Filtros aplicados:', 14, 42);
                filterInfo.forEach((info, index) => {
                    doc.text(`• ${info}`, 14, 48 + (index * 6));
                });
            }

            // Resumo
            const startY = filterInfo.length > 0 ? 60 + (filterInfo.length * 6) : 50;
            doc.setFontSize(12);
            doc.text(`Total de vendas: ${formatCurrency(summary)}`, 14, startY);
            doc.text(`Número de transações: ${sales.length}`, 14, startY + 8);

            // Preparar dados da tabela
            const tableData = sales.map(sale => [
                new Date(sale.sale_timestamp).toLocaleDateString('pt-BR'),
                clientsMap.get(sale.erp_client_id) || sale.erp_client_id,
                employeesMap.get(sale.erp_employee_id) || sale.erp_employee_id,
                packagesMap.get(sale.package_type) || sale.package_type,
                sale.quantity.toString(),
                formatCurrency(sale.total_sale_price),
                sale.payment_method
            ]);

            // Criar tabela
            autoTable(doc, {
                head: [['Data', 'Cliente', 'Vendedor', 'Pacote', 'Qtd', 'Valor', 'Pagamento']],
                body: tableData,
                startY: startY + 20,
                styles: {
                    fontSize: 8,
                    cellPadding: 2
                },
                headStyles: {
                    fillColor: [66, 139, 202],
                    textColor: 255
                },
                columnStyles: {
                    0: { cellWidth: 20 }, // Data
                    1: { cellWidth: 35 }, // Cliente
                    2: { cellWidth: 30 }, // Vendedor
                    3: { cellWidth: 25 }, // Pacote
                    4: { cellWidth: 15 }, // Qtd
                    5: { cellWidth: 25 }, // Valor
                    6: { cellWidth: 25 }  // Pagamento
                }
            });

            // Salvar o PDF
            const fileName = `relatorio-vendas-${new Date().toISOString().split('T')[0]}.pdf`;
            doc.save(fileName);

            toast.success('Relatório PDF exportado com sucesso!');
        } catch (error) {
            console.error('Erro ao exportar PDF:', error);

            // Fallback: exportar como CSV se PDF não funcionar
            try {
                const csvContent = [
                    ['Data', 'Cliente', 'Vendedor', 'Pacote', 'Quantidade', 'Valor Total', 'Pagamento'],
                    ...sales.map(sale => [
                        new Date(sale.sale_timestamp).toLocaleDateString('pt-BR'),
                        clientsMap.get(sale.erp_client_id) || sale.erp_client_id,
                        employeesMap.get(sale.erp_employee_id) || sale.erp_employee_id,
                        packagesMap.get(sale.package_type) || sale.package_type,
                        sale.quantity,
                        `R$ ${sale.total_sale_price.toFixed(2)}`,
                        sale.payment_method
                    ])
                ].map(row => row.join(';')).join('\n');

                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `relatorio-vendas-${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                toast.success('Relatório exportado como CSV (instale jspdf e jspdf-autotable para PDF)');
            } catch (csvError) {
                toast.error('Erro ao exportar relatório. Instale as dependências: pnpm add jspdf jspdf-autotable');
            }
        }
    };

    return (
        <div className="space-y-6">

            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                        <Filter className="h-5 w-5" />
                        <span>Filtros de Relatório</span>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={clearAllFilters}
                            className="ml-auto"
                        >
                            Limpar Filtros
                        </Button>
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* Filtros Ativos - Badges */}
                    {getActiveFilters().length > 0 && (
                        <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                            <div className="flex items-center gap-2 mb-2">
                                <Filter className="h-4 w-4 text-blue-600" />
                                <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                                    Filtros aplicados:
                                </span>
                            </div>
                            <div className="flex flex-wrap gap-2">
                                {getActiveFilters().map((filter) => (
                                    <Badge
                                        key={filter.key}
                                        variant="secondary"
                                        className="bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-200"
                                    >
                                        {filter.label}
                                        <X
                                            className="h-3 w-3 ml-1 cursor-pointer hover:text-blue-600"
                                            onClick={filter.onRemove}
                                        />
                                    </Badge>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Filtros de Data Pré-definidos */}
                    <div>
                        <Label className="text-sm font-medium">Período Rápido</Label>
                        <div className="flex flex-wrap gap-2 mt-2">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => applyDatePreset('today')}
                                className="h-8"
                            >
                                <CalendarDays className="h-3 w-3 mr-1" />
                                Hoje
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => applyDatePreset('yesterday')}
                                className="h-8"
                            >
                                Ontem
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => applyDatePreset('lastWeek')}
                                className="h-8"
                            >
                                Última Semana
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => applyDatePreset('lastMonth')}
                                className="h-8"
                            >
                                Último Mês
                            </Button>
                        </div>
                    </div>

                    {/* Filtros Principais - Simplificados */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <Label htmlFor="start_date">Data Inicial</Label>
                            <Input
                                type="date"
                                id="start_date"
                                value={filters.start_date}
                                onChange={e => handleFilterChange('start_date', e.target.value)}
                            />
                        </div>
                        <div>
                            <Label htmlFor="end_date">Data Final</Label>
                            <Input
                                type="date"
                                id="end_date"
                                value={filters.end_date}
                                onChange={e => handleFilterChange('end_date', e.target.value)}
                            />
                        </div>
                        <div>
                            <Label>Método de Pagamento</Label>
                            <Select value={filters.payment_method || 'all'} onValueChange={value => handleFilterChange('payment_method', value === 'all' ? '' : value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Todos os métodos" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Todos os métodos</SelectItem>
                                    <SelectItem value="Dinheiro">Dinheiro</SelectItem>
                                    <SelectItem value="PIX">PIX</SelectItem>
                                    <SelectItem value="Cartão Débito">Cartão Débito</SelectItem>
                                    <SelectItem value="Cartão Crédito">Cartão Crédito</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {/* Filtros de Busca e Seleção */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <Label htmlFor="search_text">Buscar</Label>
                            <div className="relative">
                                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="search_text"
                                    placeholder="Cliente ou vendedor..."
                                    className="pl-8"
                                    value={filters.search_text}
                                    onChange={e => handleFilterChange('search_text', e.target.value)}
                                />
                            </div>
                        </div>
                        <div>
                            <Label>Vendedor</Label>
                            <EmployeeSearchInput onSelect={handleEmployeeSelect} />
                        </div>
                        <div>
                            <Label>Cliente</Label>
                            <ClientSearchInput onSelect={handleClientSelect} />
                        </div>
                    </div>

                    {/* Ações */}
                    <div className="flex justify-between items-center pt-2">
                        <div className="flex items-center gap-2">
                            <Label className="text-sm">Itens por página:</Label>
                            <Select value={filters.per_page.toString()} onValueChange={value => handleFilterChange('per_page', parseInt(value))}>
                                <SelectTrigger className="w-20">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="10">10</SelectItem>
                                    <SelectItem value="25">25</SelectItem>
                                    <SelectItem value="50">50</SelectItem>
                                    <SelectItem value="100">100</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <Button onClick={exportToPDF}>
                            <Download className="h-4 w-4 mr-2" />
                            Exportar PDF
                        </Button>
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <CardTitle>Resumo do Período</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-2xl font-bold">Total Arrecadado: R$ {summary.toFixed(2)}</p>
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <CardTitle>Histórico de Vendas</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="overflow-x-auto">
                        <Table>
                            <TableHeader>
                            <TableRow>
                                <TableHead
                                    className="cursor-pointer hover:bg-muted/50 select-none"
                                    onClick={() => handleSortChange('sale_timestamp')}
                                >
                                    <div className="flex items-center space-x-1">
                                        <span>Data</span>
                                        <span className="text-xs">{getSortIcon('sale_timestamp')}</span>
                                    </div>
                                </TableHead>
                                <TableHead>Cliente</TableHead>
                                <TableHead>Vendedor</TableHead>
                                <TableHead>Pacote</TableHead>
                                <TableHead
                                    className="cursor-pointer hover:bg-muted/50 select-none"
                                    onClick={() => handleSortChange('quantity')}
                                >
                                    <div className="flex items-center space-x-1">
                                        <span>Qtd</span>
                                        <span className="text-xs">{getSortIcon('quantity')}</span>
                                    </div>
                                </TableHead>
                                <TableHead
                                    className="cursor-pointer hover:bg-muted/50 select-none"
                                    onClick={() => handleSortChange('total_sale_price')}
                                >
                                    <div className="flex items-center space-x-1">
                                        <span>Valor Total</span>
                                        <span className="text-xs">{getSortIcon('total_sale_price')}</span>
                                    </div>
                                </TableHead>
                                <TableHead
                                    className="cursor-pointer hover:bg-muted/50 select-none"
                                    onClick={() => handleSortChange('payment_method')}
                                >
                                    <div className="flex items-center space-x-1">
                                        <span>Pagamento</span>
                                        <span className="text-xs">{getSortIcon('payment_method')}</span>
                                    </div>
                                </TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {isLoading ? (
                                // Loading skeleton
                                Array.from({ length: filters.per_page }).map((_, index) => (
                                    <TableRow key={`skeleton-${index}`}>
                                        <TableCell>
                                            <div className="h-4 bg-muted animate-pulse rounded w-24"></div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="h-4 bg-muted animate-pulse rounded w-32"></div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="h-4 bg-muted animate-pulse rounded w-28"></div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="h-4 bg-muted animate-pulse rounded w-20"></div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="h-4 bg-muted animate-pulse rounded w-8"></div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="h-4 bg-muted animate-pulse rounded w-16"></div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="h-4 bg-muted animate-pulse rounded w-20"></div>
                                        </TableCell>
                                    </TableRow>
                                ))
                            ) : sales.length === 0 ? (
                                <TableRow><TableCell colSpan="7" className="text-center">Nenhuma venda encontrada.</TableCell></TableRow>
                            ) : (
                                sales.map((sale, index) => {
                                    // Garantir que a key seja sempre uma string válida
                                    const saleKey = sale._id?.$oid || sale._id || sale.id || `sale-${index}`;
                                    return (
                                        <TableRow key={saleKey}>
                                            <TableCell>{new Date(sale.sale_timestamp).toLocaleString('pt-BR')}</TableCell>
                                            <TableCell>{clientsMap.get(sale.erp_client_id) || sale.erp_client_id}</TableCell>
                                            <TableCell>{employeesMap.get(sale.erp_employee_id) || sale.erp_employee_id}</TableCell>
                                            <TableCell>{packagesMap.get(sale.package_type) || sale.package_type}</TableCell>
                                            <TableCell>{sale.quantity}</TableCell>
                                            <TableCell>R$ {sale.total_sale_price.toFixed(2)}</TableCell>
                                            <TableCell>{sale.payment_method}</TableCell>
                                        </TableRow>
                                    );
                                })
                            )}
                        </TableBody>
                        </Table>
                    </div>
                    <div className="flex items-center justify-between py-4">
                        <div className="text-sm text-muted-foreground">
                            Mostrando {((filters.page - 1) * filters.per_page) + 1} a {Math.min(filters.page * filters.per_page, totalSales)} de {totalSales} resultados
                        </div>
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleFilterChange('page', 1)}
                                disabled={filters.page <= 1}
                            >
                                Primeira
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleFilterChange('page', filters.page - 1)}
                                disabled={filters.page <= 1}
                            >
                                Anterior
                            </Button>
                            <span className="text-sm px-2">
                                Página {filters.page} de {totalPages}
                            </span>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleFilterChange('page', filters.page + 1)}
                                disabled={filters.page >= totalPages}
                            >
                                Próxima
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleFilterChange('page', totalPages)}
                                disabled={filters.page >= totalPages}
                            >
                                Última
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
};

export default ReportsPage; 