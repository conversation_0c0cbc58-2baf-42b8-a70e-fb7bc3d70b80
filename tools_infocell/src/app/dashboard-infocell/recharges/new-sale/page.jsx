"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import ClientSearchInput from '@/components/custom/ClientSearchInput';
import EmployeeSearchInput from '@/components/custom/EmployeeSearchInput';
import RechargeReceipt from '@/components/custom/RechargeReceipt';
import infocellApi from '@/lib/infocellApi';
import { toast } from 'sonner';
import { CheckCircle, ArrowLeft } from 'lucide-react';

const NewSalePage = () => {
    const [step, setStep] = useState(1); // 1: Seleção, 2: Recibo
    const [selectedClient, setSelectedClient] = useState(null);
    const [selectedEmployee, setSelectedEmployee] = useState(null);
    const [selectedPackage, setSelectedPackage] = useState(null);
    const [paymentMethod, setPaymentMethod] = useState('Dinheiro');
    const [quantity, setQuantity] = useState(1);
    const [availablePackages, setAvailablePackages] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [saleReceipt, setSaleReceipt] = useState(null);

    useEffect(() => {
        const fetchPackages = async () => {
            try {
                const response = await infocellApi.getRechargePackages();
                setAvailablePackages(response.data.filter(p => p.is_active));
            } catch (err) {
                toast.error(`Falha ao carregar pacotes: ${err.message}`);
            }
        };
        fetchPackages();
    }, []);

    const handlePerformSale = async () => {
        setIsLoading(true);
        try {
            const saleData = {
                erp_client_id: selectedClient.id,
                erp_employee_id: selectedEmployee.id,
                package_type: selectedPackage.package_type,
                payment_method: paymentMethod,
                quantity: parseInt(quantity, 10),
            };
            const response = await infocellApi.createRechargeSale(saleData);
            setSaleReceipt(response.data);
            setStep(2);
        } catch (err) {
            toast.error(err.message || 'Ocorreu um erro desconhecido.');
        } finally {
            setIsLoading(false);
        }
    };
    
    const resetSale = () => {
        setStep(1);
        setSelectedClient(null);
        setSelectedEmployee(null);
        setSelectedPackage(null);
        setPaymentMethod('Dinheiro');
        setQuantity(1);
        setSaleReceipt(null);
    };

    if (step === 2 && saleReceipt) {
        return (
            <RechargeReceipt 
                saleReceipt={saleReceipt}
                selectedPackage={selectedPackage}
                selectedClient={selectedClient}
                selectedEmployee={selectedEmployee}
                onNewSale={resetSale}
            />
        );
    }

    return (
        <Card className="max-w-2xl mx-auto">
            <CardHeader>
                <CardTitle>Nova Venda de Recarga</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
                <div>
                    <Label>1. Selecione o Cliente</Label>
                    <ClientSearchInput onSelect={setSelectedClient} selectedClient={selectedClient} />
                </div>

                <div>
                    <Label>2. Selecione o Vendedor</Label>
                    <EmployeeSearchInput onSelect={setSelectedEmployee} selectedEmployee={selectedEmployee} />
                </div>

                <div>
                    <Label>3. Selecione o Pacote</Label>
                    <Select onValueChange={value => setSelectedPackage(availablePackages.find(p => p._id.$oid === value))}>
                        <SelectTrigger disabled={!selectedEmployee}>
                            <SelectValue placeholder="Selecione um pacote..." />
                        </SelectTrigger>
                        <SelectContent>
                            {availablePackages.map(pkg => (
                                <SelectItem key={pkg._id.$oid} value={pkg._id.$oid}>
                                    {pkg.package_name} - R$ {pkg.sale_price.toFixed(2)}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>

                {selectedPackage && (
                    <div>
                        <Label>4. Quantidade</Label>
                        <Input
                            type="number"
                            value={quantity}
                            onChange={(e) => setQuantity(e.target.value)}
                            min="1"
                            className="mt-1"
                        />
                    </div>
                )}

                <div>
                    <Label>5. Método de Pagamento</Label>
                    <Select onValueChange={setPaymentMethod} defaultValue="Dinheiro">
                        <SelectTrigger disabled={!selectedPackage}>
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="Dinheiro">Dinheiro</SelectItem>
                            <SelectItem value="Cartão de Crédito">Cartão de Crédito</SelectItem>
                            <SelectItem value="Cartão de Débito">Cartão de Débito</SelectItem>
                            <SelectItem value="Pix">Pix</SelectItem>
                        </SelectContent>
                    </Select>
                </div>

                <Button onClick={handlePerformSale} disabled={!selectedClient || !selectedEmployee || !selectedPackage || !quantity || quantity < 1 || isLoading} className="w-full">
                    {isLoading ? 'Processando Venda...' : 'Finalizar Venda'}
                </Button>
            </CardContent>
        </Card>
    );
};

export default NewSalePage; 