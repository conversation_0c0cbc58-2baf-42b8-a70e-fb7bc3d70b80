"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import infocellApi from '@/lib/infocellApi';

export default function RechargePackagesPage() {
    const [packages, setPackages] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);

    // Form state
    const [isFormOpen, setIsFormOpen] = useState(false);
    const [currentPackage, setCurrentPackage] = useState({
        id: null,
        package_name: '',
        package_type: '',
        sale_price: 0,
        is_active: true,
    });

    const fetchPackages = async () => {
        try {
            setIsLoading(true);
            const response = await infocellApi.getRechargePackages();
            setPackages(response.data || []);
        } catch (err) {
            setError('Falha ao buscar pacotes de recarga.');
            console.error(err);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchPackages();
    }, []);

    const handleFormSubmit = async (e) => {
        e.preventDefault();
        try {
            if (currentPackage.id) {
                // Update
                await infocellApi.updateRechargePackage(currentPackage.id, {
                    ...currentPackage,
                    sale_price: parseFloat(currentPackage.sale_price)
                });
            } else {
                // Create
                await infocellApi.createRechargePackage({
                    ...currentPackage,
                    sale_price: parseFloat(currentPackage.sale_price)
                });
            }
            resetForm();
            fetchPackages();
        } catch (err) {
            setError('Falha ao salvar o pacote.');
            console.error(err);
        }
    };

    const handleEdit = (pkg) => {
        setCurrentPackage({ ...pkg, id: pkg._id });
        setIsFormOpen(true);
    };

    const handleDelete = async (packageId) => {
        if (window.confirm('Tem certeza que deseja excluir este pacote?')) {
            try {
                await infocellApi.deleteRechargePackage(packageId);
                fetchPackages();
            } catch (err) {
                // A mensagem de erro agora virá do backend com a razão exata
                setError(err.message || 'Falha ao excluir o pacote.');
                console.error(err);
            }
        }
    };

    const resetForm = () => {
        setIsFormOpen(false);
        setCurrentPackage({
            id: null,
            package_name: '',
            package_type: '',
            sale_price: 0,
            is_active: true,
        });
    };

    return (
        <div className="space-y-4">
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>Pacotes de Recarga</CardTitle>
                    <Button onClick={() => { resetForm(); setIsFormOpen(true); }}>
                        Novo Pacote
                    </Button>
                </CardHeader>
                <CardContent>
                    {isLoading && <p>Carregando...</p>}
                    {error && <p className="text-red-500">{error}</p>}

                    {isFormOpen && (
                        <form onSubmit={handleFormSubmit} className="space-y-4 mb-6 p-4 border rounded-lg">
                            <h3 className="text-lg font-semibold">{currentPackage.id ? 'Editar Pacote' : 'Novo Pacote'}</h3>
                            <div>
                                <Label htmlFor="package_name">Nome do Pacote</Label>
                                <Input id="package_name" value={currentPackage.package_name} onChange={(e) => setCurrentPackage({ ...currentPackage, package_name: e.target.value })} required />
                            </div>
                            <div>
                                <Label htmlFor="package_type">Tipo (ex: tv, filmes, combo)</Label>
                                <Input id="package_type" value={currentPackage.package_type} onChange={(e) => setCurrentPackage({ ...currentPackage, package_type: e.target.value })} required />
                            </div>
                            <div>
                                <Label htmlFor="sale_price">Preço de Venda</Label>
                                <Input id="sale_price" type="number" step="0.01" value={currentPackage.sale_price} onChange={(e) => setCurrentPackage({ ...currentPackage, sale_price: e.target.value })} required />
                            </div>
                            <div className="flex items-center space-x-2">
                                <Switch id="is_active" checked={currentPackage.is_active} onCheckedChange={(checked) => setCurrentPackage({ ...currentPackage, is_active: checked })} />
                                <Label htmlFor="is_active">Ativo</Label>
                            </div>
                            <div className="flex justify-end space-x-2">
                                <Button type="button" variant="outline" onClick={resetForm}>Cancelar</Button>
                                <Button type="submit">Salvar</Button>
                            </div>
                        </form>
                    )}

                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nome</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Preço</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {packages.map((pkg) => (
                                    <tr key={pkg._id.$oid}>
                                        <td className="px-6 py-4 whitespace-nowrap">{pkg.package_name}</td>
                                        <td className="px-6 py-4 whitespace-nowrap">{pkg.package_type}</td>
                                        <td className="px-6 py-4 whitespace-nowrap">R$ {pkg.sale_price.toFixed(2)}</td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${pkg.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                                {pkg.is_active ? 'Ativo' : 'Inativo'}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                            <Button variant="outline" size="sm" onClick={() => handleEdit(pkg)}>Editar</Button>
                                            <Button variant="destructive" size="sm" onClick={() => handleDelete(pkg._id.$oid)}>Excluir</Button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                </CardContent>
            </Card>
        </div>
    );
} 