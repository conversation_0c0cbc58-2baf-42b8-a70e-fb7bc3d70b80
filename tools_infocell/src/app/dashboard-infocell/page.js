import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { FileText, FolderOpen, Zap, Archive } from "lucide-react";
import Link from "next/link";
import RecentDocuments from "@/components/documents/RecentDocuments";

export default function Page() {
  const tools = [
    {
      title: "Gerar Documento",
      description:
        "Gere documentos personalizados usando templates e dados de clientes.",
      icon: Zap,
      link: "/dashboard-infocell/gerar-documento",
    },
    {
      title: "Gerenciar Templates",
      description:
        "Faça upload e gerencie seus templates de documentos compatíveis com Carbone.js.",
      icon: FolderOpen,
      link: "/dashboard-infocell/templates",
    },
    {
      title: "Documentos Gerados",
      description:
        "Visualize e gerencie o histórico de documentos criados no sistema.",
      icon: Archive,
      link: "/dashboard-infocell/documentos",
    },
    {
      title: "Gerador de Currí<PERSON>lo (Legado)",
      description:
        "Sistema anterior de geração de currículos (será removido em breve).",
      icon: FileText,
      link: "/dashboard-infocell/resume-generator",
    },
  ];

  return (
    <div className="space-y-8">
      <header>
        <h1 className="text-3xl font-bold tracking-tight text-foreground">
          Dashboard
        </h1>
      </header>

      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <div className="grid gap-6 md:grid-cols-2">
            {tools.map((tool, index) => (
              <Card key={index} className="group hover:shadow-md transition-all duration-200 border shadow">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors">
                      <tool.icon className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle className="text-lg">{tool.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="pb-6">
                  <CardDescription className="text-sm leading-relaxed">
                    {tool.description}
                  </CardDescription>
                </CardContent>
                <CardFooter>
                  <Link href={tool.link} passHref className="w-full">
                    <Button className="w-full" variant="outline">
                      Acessar
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
        
        <div className="lg:col-span-1">
          <RecentDocuments />
        </div>
      </div>
    </div>
  );
}
