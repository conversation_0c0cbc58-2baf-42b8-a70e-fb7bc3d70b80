{"name": "tools_infocell", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "pre-deploy": "next build && next start", "vercel-build": "next build"}, "dependencies": {"@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@sparticuz/chromium": "^133.0.0", "@vercel/analytics": "^1.5.0", "@vercel/blob": "^1.0.1", "bcryptjs": "^3.0.2", "chrome-aws-lambda": "10.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "html2canvas": "^1.4.1", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.508.0", "next": "15.3.2", "next-themes": "^0.4.6", "puppeteer-core": "10.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "redis": "^5.0.1", "sonner": "^2.0.5", "tailwind-merge": "^3.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tw-animate-css": "^1.2.9"}}